<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>cn.dev33</groupId>
	<artifactId>sa-token-demo-sso2-client</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	
	<!-- SpringBoot -->
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.5.14</version>
		<relativePath/>
	</parent>
	
	<!-- 定义 Sa-Token 版本号 -->
	<properties>
		<sa-token.version>1.44.0</sa-token.version>
	</properties>

	<dependencies>

		<!-- SpringBoot依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		
		<!-- Sa-Token 权限认证, 在线文档：https://sa-token.cc/ -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        
		<!-- Sa-Token 插件：整合SSO -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-sso</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        
		<!-- Sa-Token 整合 RedisTemplate -->
		<dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-template</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        
		<!-- 提供Redis连接池 -->
		<dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        
		<!-- Sa-Token插件：权限缓存与业务缓存分离 -->
		<dependency>
		    <groupId>cn.dev33</groupId>
		    <artifactId>sa-token-alone-redis</artifactId>
            <version>${sa-token.version}</version>
		</dependency>

		<!-- Sa-Token 插件：整合 Forest 请求工具 -->
		<dependency>
			<groupId>cn.dev33</groupId>
			<artifactId>sa-token-forest</artifactId>
			<version>${sa-token.version}</version>
		</dependency>

	</dependencies>
	
	
</project>