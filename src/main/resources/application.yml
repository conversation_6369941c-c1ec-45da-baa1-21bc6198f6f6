# 端口
server:
    port: 9002

# sa-token配置 
sa-token:
    # 打印操作日志
    is-log: true

    # SSO-相关配置
    sso-client:
        # 应用标识
        client: sso-client2
        # SSO-Server 端主机地址
        server-url: http://sa-sso-server.com:8080
        # 在 sso-server 端前后端分离时需要单独配置 auth-url 参数（上面的不要注释，auth-url 配置项和 server-url 要同时存在）
        # auth-url: http://127.0.0.1:8848/sa-token-demo-sso-server-h5/sso-auth.html
        # API 接口调用秘钥 (单点注销时会用到)
        secret-key: SSO-C2-kQwIOrYvnXmSDkwEiFngrKidMcdrgKor
        # 在 sso-server 端前后端分离时需要单独配置 auth-url 参数（上面的不要注释，auth-url 配置项和 server-url 要同时存在）
        auth-url: http://127.0.0.1:8848/sa-token-demo-sso-server-h5//sso-auth.html
        is-slo: true
    
    # 配置 Sa-Token 单独使用的Redis连接（此处需要和 SSO-Server 端连接同一个 Redis）
    # 注：使用 alone-redis 需要在 pom.xml 引入 sa-token-alone-redis 依赖
    alone-redis:
        # Redis数据库索引
        database: 1
        # Redis服务器地址
        host: 127.0.0.1
        # Redis服务器连接端口
        port: 6379
        # Redis服务器连接密码（默认为空）
        password: 123456
        # 连接超时时间
        timeout: 10s
        lettuce:
            pool:
                # 连接池最大连接数
                max-active: 200
                # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms
                # 连接池中的最大空闲连接
                max-idle: 10
                # 连接池中的最小空闲连接
                min-idle: 0

forest:
    # 关闭 forest 请求日志打印
    log-enabled: false
